{% extends "base.html" %}

{% block title %}Edit Book - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-3"></i>Edit Book</h2>
    <a href="{{ url_for('admin_books') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Books
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-book me-2"></i>Book Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">
                                <i class="fas fa-heading me-2"></i>Book Title
                            </label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="{{ book.title }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="author" class="form-label">
                                <i class="fas fa-user-edit me-2"></i>Author
                            </label>
                            <input type="text" class="form-control" id="author" name="author" 
                                   value="{{ book.author }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">
                                <i class="fas fa-tags me-2"></i>Category
                            </label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="Fiction" {% if book.category == 'Fiction' %}selected{% endif %}>Fiction</option>
                                <option value="Non-Fiction" {% if book.category == 'Non-Fiction' %}selected{% endif %}>Non-Fiction</option>
                                <option value="Science" {% if book.category == 'Science' %}selected{% endif %}>Science</option>
                                <option value="Technology" {% if book.category == 'Technology' %}selected{% endif %}>Technology</option>
                                <option value="History" {% if book.category == 'History' %}selected{% endif %}>History</option>
                                <option value="Biography" {% if book.category == 'Biography' %}selected{% endif %}>Biography</option>
                                <option value="Education" {% if book.category == 'Education' %}selected{% endif %}>Education</option>
                                <option value="Reference" {% if book.category == 'Reference' %}selected{% endif %}>Reference</option>
                                <option value="Children" {% if book.category == 'Children' %}selected{% endif %}>Children</option>
                                <option value="Romance" {% if book.category == 'Romance' %}selected{% endif %}>Romance</option>
                                <option value="Mystery" {% if book.category == 'Mystery' %}selected{% endif %}>Mystery</option>
                                <option value="Fantasy" {% if book.category == 'Fantasy' %}selected{% endif %}>Fantasy</option>
                                <option value="Other" {% if book.category == 'Other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="quantity" class="form-label">
                                <i class="fas fa-sort-numeric-up me-2"></i>Quantity
                            </label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   value="{{ book.quantity }}" min="1" required>
                            <div class="form-text">
                                Currently issued: {{ book.quantity - book.available_count }} copies
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Changing the quantity will automatically adjust the available count. 
                        Current available: {{ book.available_count }} copies
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-custom me-3">
                            <i class="fas fa-save me-2"></i>Update Book
                        </button>
                        <a href="{{ url_for('admin_books') }}" class="btn btn-secondary btn-custom">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Book Details Summary -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-book fa-2x text-primary mb-2"></i>
                <h5>Book ID</h5>
                <h4 class="text-primary">{{ book.book_id }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h5>Available</h5>
                <h4 class="text-success">{{ book.available_count }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-exchange-alt fa-2x text-warning mb-2"></i>
                <h5>Issued</h5>
                <h4 class="text-warning">{{ book.quantity - book.available_count }}</h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Warn if reducing quantity below issued copies
    var originalQuantity = {{ book.quantity }};
    var issuedCopies = {{ book.quantity - book.available_count }};
    
    $('#quantity').on('change', function() {
        var newQuantity = parseInt($(this).val());
        
        if (newQuantity < issuedCopies) {
            alert('Warning: You cannot set quantity below the number of issued copies (' + issuedCopies + ')');
            $(this).val(originalQuantity);
        }
    });
});
</script>
{% endblock %}
