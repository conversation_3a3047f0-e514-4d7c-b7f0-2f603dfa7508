<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Smart Library Management System - Development Instructions

## Project Overview
This is a Flask-based Library Management System with role-based access control for Admin, Librarian, and Student users. The system uses SQLite database and Bootstrap frontend.

## Key Technologies
- **Backend**: Python Flask with SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, Bootstrap 5, jQuery
- **Database**: SQLite (can be configured for MySQL)
- **Authentication**: Session-based with password hashing

## Code Conventions

### Python (Flask)
- Use SQLAlchemy models for database operations
- Follow Flask best practices for route definitions
- Use session management for authentication
- Implement proper error handling and flash messages
- Use Werkzeug for password hashing

### HTML Templates
- Extend from `base.html` template
- Use Bootstrap 5 classes for styling
- Include Font Awesome icons consistently
- Implement responsive design patterns
- Use Jinja2 template syntax for dynamic content

### Database Design
- Follow the existing schema with proper foreign key relationships
- Use appropriate data types and constraints
- Implement cascade operations where necessary
- Consider indexing for performance

## Security Guidelines
- Always hash passwords before storing
- Validate user roles before allowing access to routes
- Use session management for authentication state
- Implement CSRF protection for forms
- Sanitize user inputs

## UI/UX Guidelines
- Maintain consistent color scheme (primary: blue, success: green, danger: red)
- Use card-based layouts for content organization
- Implement hover effects and transitions
- Ensure mobile responsiveness
- Provide clear feedback for user actions

## File Organization
- Templates in `/templates/` directory
- Static files in `/static/` directory (if needed)
- Main application logic in `app.py`
- Database models defined in the main application file
- Requirements listed in `requirements.txt`

## Common Patterns

### Route Protection
```python
if session.get('user_role') != 'expected_role':
    return redirect(url_for('login', role='expected_role'))
```

### Form Handling
- Use POST methods for data modification
- Implement proper validation
- Provide user feedback through flash messages
- Redirect after successful operations

### Template Structure
- Use consistent sidebar navigation
- Include breadcrumb navigation where appropriate
- Implement proper error and success message display
- Use modals for confirmations

## Testing Considerations
- Test all user roles and their permissions
- Verify database operations work correctly
- Check form validation and error handling
- Ensure responsive design works on different screen sizes

When making changes or additions to this codebase, follow these established patterns and maintain consistency with the existing implementation.
