{% extends "base.html" %}

{% block title %}Add Student - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link active" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-3"></i>Add New Student</h2>
    <a href="{{ url_for('admin_students') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Students
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Student Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- User ID and Username -->
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">
                                <i class="fas fa-id-card me-2"></i>User ID (Roll No/Staff ID) <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="user_id" name="user_id" required placeholder="e.g., 21CS001 or STAFF001">
                            <div class="form-text">For students: Enter roll number. For staff: Enter staff ID</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>Username <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required placeholder="e.g., john_doe">
                            <div class="form-text">Login username (no spaces)</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Name and Email -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required placeholder="Enter full name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Email and Password -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required placeholder="Enter password">
                                <button class="btn btn-outline-secondary" type="button" id="generatePassword">
                                    <i class="fas fa-magic"></i> Generate
                                </button>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Or use auto-generate (name+rollnumber format)</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Department and Designation -->
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">
                                <i class="fas fa-building me-2"></i>Department <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="department" name="department" required>
                                <option value="">Select Department</option>
                                {% for dept_code, dept_name in departments %}
                                <option value="{{ dept_code }}">{{ dept_code }} - {{ dept_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="designation" class="form-label">
                                <i class="fas fa-user-tag me-2"></i>Designation <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="designation" name="designation" required>
                                <option value="">Select Designation</option>
                                {% for design_code, design_name in designations %}
                                <option value="{{ design_code }}">{{ design_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- College and Course -->
                        <div class="col-md-6 mb-3">
                            <label for="college" class="form-label">
                                <i class="fas fa-school me-2"></i>College
                            </label>
                            <input type="text" class="form-control" id="college" name="college" value="Engineering College" placeholder="College name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="course" class="form-label">
                                <i class="fas fa-graduation-cap me-2"></i>Course <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="course" name="course" required placeholder="e.g., B.Tech Computer Science">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date of Birth and Current Year -->
                        <div class="col-md-6 mb-3">
                            <label for="dob" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Date of Birth <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="dob" name="dob" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="current_year" class="form-label">
                                <i class="fas fa-calendar-check me-2"></i>Current Year <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="current_year" name="current_year" required>
                                <option value="">Select Year</option>
                                <option value="1">1st Year</option>
                                <option value="2">2nd Year</option>
                                <option value="3">3rd Year</option>
                                <option value="4">4th Year</option>
                                <option value="5">5th Year</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Validity Date -->
                        <div class="col-md-6 mb-3">
                            <label for="validity_date" class="form-label">
                                <i class="fas fa-calendar-times me-2"></i>Validity Date <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="validity_date" name="validity_date" required>
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Account will be automatically deleted after this date
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="setDefaultValidity()">
                                <i class="fas fa-calendar-plus me-2"></i>Set Default (1 Year)
                            </button>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin_students') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Add Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Quick Tips:</h6>
                    <ul class="mb-0 small">
                        <li>User ID should be unique (e.g., STU001, STU002)</li>
                        <li>Username for login (no spaces allowed)</li>
                        <li>Auto-generate password using userid+name format</li>
                        <li>Choose appropriate department from dropdown</li>
                        <li>Validity date determines account expiry</li>
                        <li>Expired accounts are automatically deleted</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important:</h6>
                    <ul class="mb-0 small">
                        <li>All fields marked with <span class="text-danger">*</span> are required</li>
                        <li>Email addresses must be unique</li>
                        <li>Roll numbers must be unique</li>
                        <li>Validity date must be in the future</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate password
    $('#generatePassword').click(function() {
        const name = $('#name').val().replace(/[^a-zA-Z]/g, '').toLowerCase();
        const userId = $('#user_id').val().replace(/[^a-zA-Z0-9]/g, '');
        
        if (name && userId) {
            $('#password').val(userId + name);
        } else {
            alert('Please enter both user ID and name first.');
        }
    });
    
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Auto-fill course based on department selection
    $('#department').change(function() {
        const department = $(this).val();
        const departmentText = $(this).find('option:selected').text();
        
        if (department) {
            const courseName = departmentText.split(' - ')[1];
            $('#course').val('B.Tech ' + courseName);
        }
    });
    
    // Auto-generate username from name
    $('#name').blur(function() {
        const name = $(this).val().toLowerCase().replace(/[^a-zA-Z\s]/g, '').replace(/\s+/g, '_');
        if (name && !$('#username').val()) {
            $('#username').val(name);
        }
    });
});

function setDefaultValidity() {
    const today = new Date();
    today.setFullYear(today.getFullYear() + 1); // Add 1 year
    const defaultDate = today.toISOString().split('T')[0];
    $('#validity_date').val(defaultDate);
}
</script>
{% endblock %}
