{% extends "base.html" %}

{% block title %}Circulation Tracking - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('admin_circulation') }}">
    <i class="fas fa-exchange-alt me-2"></i>Circulation
</a>
<a class="nav-link" href="{{ url_for('admin_reports') }}">
    <i class="fas fa-chart-bar me-2"></i>Reports
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-exchange-alt me-3"></i>Circulation Tracking</h2>
    <div>
        <a href="{{ url_for('admin_reports') }}" class="btn btn-primary btn-custom">
            <i class="fas fa-chart-bar me-2"></i>Generate Reports
        </a>
    </div>
</div>

<!-- Circulation Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ data.total_issued }}</h3>
                        <p class="text-muted mb-0">Currently Issued</p>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-book-open fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ data.total_overdue }}</h3>
                        <p class="text-muted mb-0">Overdue Books</p>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ data.recent_issues }}</h3>
                        <p class="text-muted mb-0">Issues (7 days)</p>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ data.recent_returns }}</h3>
                        <p class="text-muted mb-0">Returns (7 days)</p>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Books and Active Students -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-star me-2"></i>Most Popular Books</h5>
            </div>
            <div class="card-body">
                {% if data.popular_books %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Book</th>
                                <th>Issues</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for book in data.popular_books %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ book.title }}</strong><br>
                                    <small class="text-muted">by {{ book.author }} ({{ book.access_no }})</small>
                                </td>
                                <td><span class="badge bg-primary">{{ book.issue_count }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No circulation data available</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Most Active Students</h5>
            </div>
            <div class="card-body">
                {% if data.active_students %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Student</th>
                                <th>Books</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in data.active_students %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ student.name }}</strong><br>
                                    <small class="text-muted">{{ student.user_id }} - {{ student.department }}</small>
                                </td>
                                <td><span class="badge bg-success">{{ student.active_books }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No active students</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Department-wise Circulation -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Department-wise Circulation</h5>
            </div>
            <div class="card-body">
                {% if data.dept_circulation %}
                <div class="row">
                    {% for dept in data.dept_circulation %}
                    <div class="col-md-2 col-sm-4 col-6 mb-3">
                        <div class="text-center">
                            <div class="h4 text-primary">{{ dept.total_issues }}</div>
                            <div class="text-muted">{{ dept.department }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">No circulation data by department</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}
