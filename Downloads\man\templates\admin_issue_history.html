{% extends "base.html" %}

{% block title %}Issue History - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link active" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-history me-3"></i>Issue History</h2>
    <div>
        <button class="btn btn-outline-primary btn-custom me-2" onclick="filterIssues('all')">
            <i class="fas fa-list me-2"></i>All Issues
        </button>
        <button class="btn btn-outline-warning btn-custom me-2" onclick="filterIssues('active')">
            <i class="fas fa-clock me-2"></i>Active Issues
        </button>
        <button class="btn btn-outline-success btn-custom" onclick="filterIssues('returned')">
            <i class="fas fa-check me-2"></i>Returned
        </button>
    </div>
</div>

<!-- Issue Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card stat-card-primary">
            <div class="card-body text-center">
                <i class="fas fa-list fa-2x text-primary mb-2"></i>
                <h4>{{ issues|length }}</h4>
                <p class="text-muted mb-0">Total Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card stat-card-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4>{{ issues|selectattr('return_date', 'none')|list|length }}</h4>
                <p class="text-muted mb-0">Active Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-check fa-2x text-success mb-2"></i>
                <h4>{{ issues|rejectattr('return_date', 'none')|list|length }}</h4>
                <p class="text-muted mb-0">Returned</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card stat-card-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4>0</h4>
                <p class="text-muted mb-0">Overdue</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Book Issue Records</h5>
    </div>
    <div class="card-body">
        {% if issues %}
        <div class="table-responsive">
            <table class="table table-hover" id="issuesTable">
                <thead class="table-dark">
                    <tr>
                        <th>Issue ID</th>
                        <th>Student</th>
                        <th>Book</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Return Date</th>
                        <th>Status</th>
                        <th>Fine</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr class="issue-row {% if issue.return_date %}returned{% else %}active{% endif %}">
                        <td><strong>#{{ issue.id }}</strong></td>
                        <td>
                            <div>
                                <strong>{{ issue.student.name }}</strong>
                                <br>
                                <small class="text-muted">{{ issue.student.roll_number }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ issue.book.title }}</strong>
                                <br>
                                <small class="text-muted">Access No: {{ issue.book.access_no }}</small>
                            </div>
                        </td>
                        <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ issue.due_date.strftime('%Y-%m-%d') if issue.due_date else 'N/A' }}</td>
                        <td>
                            {% if issue.return_date %}
                                {{ issue.return_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">Not returned</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.return_date %}
                                <span class="badge bg-success">Returned</span>
                            {% else %}
                                <span class="badge bg-warning">Active</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.fine_amount and issue.fine_amount > 0 %}
                                <span class="text-danger">₹{{ "%.2f"|format(issue.fine_amount) }}</span>
                            {% else %}
                                <span class="text-success">₹0.00</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not issue.return_date %}
                                <button class="btn btn-sm btn-outline-success me-1" 
                                        onclick="returnBook({{ issue.id }})" 
                                        title="Mark as Returned">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="renewBook({{ issue.id }})" 
                                        title="Renew Book">
                                    <i class="fas fa-redo"></i>
                                </button>
                            {% else %}
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="viewIssue({{ issue.id }})" 
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-history fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No issue records found</h5>
            <p class="text-muted">Issue records will appear here once books are issued to students.</p>
            <a href="{{ url_for('admin_books') }}" class="btn btn-primary">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterIssues(type) {
    const rows = document.querySelectorAll('.issue-row');
    
    rows.forEach(row => {
        if (type === 'all') {
            row.style.display = '';
        } else if (type === 'active') {
            row.style.display = row.classList.contains('active') ? '' : 'none';
        } else if (type === 'returned') {
            row.style.display = row.classList.contains('returned') ? '' : 'none';
        }
    });
    
    // Update button states
    document.querySelectorAll('.btn-outline-primary, .btn-outline-warning, .btn-outline-success').forEach(btn => {
        btn.classList.remove('active');
    });
    
    if (type === 'all') {
        document.querySelector('.btn-outline-primary').classList.add('active');
    } else if (type === 'active') {
        document.querySelector('.btn-outline-warning').classList.add('active');
    } else if (type === 'returned') {
        document.querySelector('.btn-outline-success').classList.add('active');
    }
}

function returnBook(issueId) {
    if (confirm('Mark this book as returned?')) {
        // TODO: Implement return book functionality
        alert('Return book functionality to be implemented');
        // Example: POST to /admin/return_book/issueId
    }
}

function renewBook(issueId) {
    if (confirm('Renew this book issue?')) {
        // TODO: Implement renew book functionality
        alert('Renew book functionality to be implemented');
        // Example: POST to /admin/renew_book/issueId
    }
}

function viewIssue(issueId) {
    // TODO: Implement view issue details
    alert('View issue details functionality to be implemented');
    // Example: Show modal with detailed issue information
}

// Initialize with all issues shown
document.addEventListener('DOMContentLoaded', function() {
    filterIssues('all');
});
</script>
{% endblock %}
