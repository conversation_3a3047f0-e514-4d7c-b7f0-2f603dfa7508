{% extends "base.html" %}

{% block title %}Manage Librarians - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link active" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-tie me-3"></i>Manage Librarians</h2>
    <a href="{{ url_for('admin_add_librarian') }}" class="btn btn-primary btn-custom">
        <i class="fas fa-plus me-2"></i>Add New Librarian
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Librarian Accounts</h5>
    </div>
    <div class="card-body">
        {% if librarians %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for librarian in librarians %}
                    <tr>
                        <td>{{ librarian.id }}</td>
                        <td><strong>{{ librarian.name }}</strong></td>
                        <td>{{ librarian.email }}</td>
                        <td>
                            <span class="badge bg-success">Active</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteLibrarian({{ librarian.id }}, '{{ librarian.name }}')" 
                                    title="Delete Librarian">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No librarians found</h5>
            <p class="text-muted">Start by adding your first librarian account.</p>
            <a href="{{ url_for('admin_add_librarian') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Librarian
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteLibrarian(id, name) {
    if (confirm(`Are you sure you want to delete librarian "${name}"? This action cannot be undone.`)) {
        // Create a form and submit it
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/delete_librarian/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
