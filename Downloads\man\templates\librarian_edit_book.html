{% extends "base.html" %}

{% block title %}Edit Book - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-3"></i>Edit Book</h2>
    <a href="{{ url_for('librarian_books') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Books
    </a>
</div>

<form method="POST" class="card p-4">
    <div class="mb-3">
        <label class="form-label">Title</label>
        <input type="text" class="form-control" name="title" value="{{ book.title }}" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Publisher</label>
        <input type="text" class="form-control" name="publisher" value="{{ book.publisher }}" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Subject</label>
        <input type="text" class="form-control" name="subject" value="{{ book.subject }}" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Department</label>
        <input type="text" class="form-control" name="department" value="{{ book.department }}" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Location</label>
        <input type="text" class="form-control" name="location" value="{{ book.location }}" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Copies</label>
        <input type="number" class="form-control" name="copies" value="{{ book.copies }}" min="1" required>
    </div>
    <div class="d-grid">
        <button type="submit" class="btn btn-success">
            <i class="fas fa-save me-2"></i>Save Changes
        </button>
    </div>
</form>
{% endblock %}
