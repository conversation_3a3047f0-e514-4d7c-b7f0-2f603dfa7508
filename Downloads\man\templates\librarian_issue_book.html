{% extends "base.html" %}

{% block title %}Issue Book{% endblock %}

{% block sidebar %}
    {# This block overrides the default sidebar in base.html to set the active link #}
    <a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
    </a>
    <a class="nav-link active" href="{{ url_for('librarian_issue_book') }}">
        <i class="fas fa-book-reader me-2"></i>Issue Book
    </a>
    <a class="nav-link" href="{{ url_for('librarian_return_book') }}">
        <i class="fas fa-book me-2"></i>Return Book
    </a>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-book-reader me-2"></i>Issue Book</h2>

    <div class="card mb-4">
        <div class="card-body">
            <form id="issueForm" method="POST" action="{{ url_for('librarian_issue_book') }}" class="row g-3">
                <div class="col-md-6">
                    <label for="book_id" class="form-label">
                        <i class="fas fa-book me-2"></i>Select Book
                    </label>
                    <select class="form-select" id="book_id" name="book_id" required>
                        <option value="" disabled selected>Choose a book from the list or click a card below...</option>
                        {% for book in books %}
                            <option value="{{ book.book_id }}">
                                {{ book.title }} by {{ book.author }} ({{ book.available_count }} available)
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="student_user_id" class="form-label">
                        <i class="fas fa-user me-2"></i>Student/Staff User ID
                    </label>
                    <input type="text" class="form-control" id="student_user_id" name="student_user_id" required placeholder="Enter Roll No/Staff ID">
                </div>

                <div class="col-12">
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Loan Period:</strong> Books are issued for 14 days.
                        <strong>Fine:</strong> A fine of ₹1.00 per day will be charged for overdue books.
                    </div>
                </div>

                <div class="col-12 text-center mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check me-2"></i>Issue Book
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Click to Select an Available Book</h5>
        </div>
        <div class="card-body">
            {% if books %}
            <div class="row">
                {% for book in books[:6] %} {# Show a preview of the first 6 books #}
                <div class="col-md-4 mb-3">
                    <div class="card book-preview h-100" data-book-id="{{ book.book_id }}">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-1">{{ book.title }}</h6>
                            <p class="card-text text-muted mb-1">{{ book.author }}</p>
                            <span class="badge bg-success-soft text-success">{{ book.available_count }} available</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if books|length > 6 %}
            <div class="text-center">
                <small class="text-muted">And {{ books|length - 6 }} more available in the dropdown...</small>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-3">
                <p class="text-muted">No books are currently available for issue.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .book-preview {
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        border: 2px solid transparent;
    }
    .book-preview:hover {
        border-color: var(--bs-primary);
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .book-preview.selected {
        border-color: var(--bs-primary);
        background-color: #e9ecef;
    }
    .bg-success-soft {
        background-color: rgba(25, 135, 84, 0.15);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const bookSelect = $('#book_id');
    const bookPreviews = $('.book-preview');

    // Function to visually update which card is selected
    function updateSelectedCard() {
        const selectedId = bookSelect.val();
        bookPreviews.each(function() {
            // Check the data attribute we added in the HTML
            if ($(this).data('book-id') == selectedId) {
                $(this).addClass('selected');
            } else {
                $(this).removeClass('selected');
            }
        });
    }

    // When a book preview card is clicked
    bookPreviews.on('click', function() {
        const bookId = $(this).data('book-id');
        // Set the dropdown's value and trigger its 'change' event
        bookSelect.val(bookId).trigger('change');
        // For better UX, move focus to the next input field
        $('#student_user_id').focus();
    });

    // When the dropdown value changes (either by card click or manually)
    bookSelect.on('change', function() {
        updateSelectedCard();
    });

    // Clear selection visual when form is reset
    $('#issueForm').on('reset', function() {
        // Use a small timeout to allow the reset to complete
        setTimeout(() => {
            bookSelect.val('');
            updateSelectedCard();
        }, 50);
    });

    // Form validation before submission
    $('#issueForm').on('submit', function(e) {
        if (!bookSelect.val()) {
            e.preventDefault();
            alert('Please select a book.');
            return;
        }
        if (!$('#student_user_id').val().trim()) {
            e.preventDefault();
            alert('Please enter the Student or Staff User ID.');
        }
    });

    // Set initial state on page load
    updateSelectedCard();
});
</script>
{% endblock %}