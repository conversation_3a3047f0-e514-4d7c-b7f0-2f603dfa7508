{% extends "base.html" %}

{% block title %}Student Profile{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('student_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('student_search_books') }}">
    <i class="fas fa-search me-2"></i>Search Books
</a>
<a class="nav-link" href="{{ url_for('student_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
<a class="nav-link active" href="{{ url_for('student_profile') }}">
    <i class="fas fa-user me-2"></i>My Profile
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user me-3"></i>My Profile</h2>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Update Profile Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Full Name
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ student.name }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="roll_number" class="form-label">
                                <i class="fas fa-id-card me-2"></i>Roll Number
                            </label>
                            <input type="text" class="form-control" id="roll_number" name="roll_number" 
                                   value="{{ student.roll_number }}" readonly>
                            <div class="form-text">Roll number cannot be changed</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="{{ student.email }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>New Password (Leave blank to keep current)
                        </label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Enter new password or leave blank">
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-custom me-3">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                        <a href="{{ url_for('student_dashboard') }}" class="btn btn-secondary btn-custom">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Profile Summary -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-user-circle fa-3x text-primary mb-2"></i>
                <h5>{{ student.name }}</h5>
                <p class="text-muted">{{ student.roll_number }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-envelope fa-2x text-info mb-2"></i>
                <h6>Email</h6>
                <p class="text-muted">{{ student.email }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                <h6>Status</h6>
                <span class="badge bg-success">Active Student</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
