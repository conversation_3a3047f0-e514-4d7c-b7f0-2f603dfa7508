{% extends "base.html" %}

{% block title %}Library Settings - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link active" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cog me-3"></i>Library Settings</h2>
</div>

<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

<!-- Settings Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-sliders-h"></i> Library Policy Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="student_book_limit" class="form-label">
                                    <i class="fas fa-user-graduate"></i> Student Book Limit
                                </label>
                                <input type="number" class="form-control" id="student_book_limit" 
                                       name="student_book_limit" value="{{ settings.student_book_limit }}" 
                                       min="1" max="20" required>
                                <small class="form-text text-muted">Maximum books a student can issue at once</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="staff_book_limit" class="form-label">
                                    <i class="fas fa-user-tie"></i> Staff Book Limit
                                </label>
                                <input type="number" class="form-control" id="staff_book_limit" 
                                       name="staff_book_limit" value="{{ settings.staff_book_limit }}" 
                                       min="1" max="50" required>
                                <small class="form-text text-muted">Maximum books a staff member can issue at once</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_issue_days" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Default Issue Days
                                </label>
                                <input type="number" class="form-control" id="default_issue_days" 
                                       name="default_issue_days" value="{{ settings.default_issue_days }}" 
                                       min="1" max="365" required>
                                <small class="form-text text-muted">Default number of days for book issue</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="renewal_limit" class="form-label">
                                    <i class="fas fa-redo"></i> Renewal Limit
                                </label>
                                <input type="number" class="form-control" id="renewal_limit" 
                                       name="renewal_limit" value="{{ settings.renewal_limit }}" 
                                       min="0" max="10" required>
                                <small class="form-text text-muted">Maximum number of times a book can be renewed</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fine_per_day" class="form-label">
                                    <i class="fas fa-rupee-sign"></i> Fine Per Day (₹)
                                </label>
                                <input type="number" class="form-control" id="fine_per_day" 
                                       name="fine_per_day" value="{{ settings.fine_per_day }}" 
                                       min="0" max="100" step="0.50" required>
                                <small class="form-text text-muted">Fine amount per day for overdue books</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Settings Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0"><i class="fas fa-info-circle"></i> Settings Information</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Current Settings:</strong>
                    <ul class="list-unstyled mt-2">
                        <li><i class="fas fa-user-graduate text-primary"></i> Student Limit: <strong>{{ settings.student_book_limit }}</strong> books</li>
                        <li><i class="fas fa-user-tie text-success"></i> Staff Limit: <strong>{{ settings.staff_book_limit }}</strong> books</li>
                        <li><i class="fas fa-calendar-alt text-info"></i> Issue Days: <strong>{{ settings.default_issue_days }}</strong> days</li>
                        <li><i class="fas fa-redo text-warning"></i> Renewals: <strong>{{ settings.renewal_limit }}</strong> times</li>
                        <li><i class="fas fa-rupee-sign text-danger"></i> Fine: <strong>₹{{ settings.fine_per_day }}</strong>/day</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Note:</strong> Changes to these settings will apply to all new book issues. Existing issues will retain their original terms.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
