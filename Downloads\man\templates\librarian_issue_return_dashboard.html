{% extends "base.html" %}
{% block title %}Issue/Return Books - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('librarian_issue_return_dashboard') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-exchange-alt me-2"></i>Issue/Return Books</h2>
    <div class="card mb-4">
        <div class="card-body">
            <form id="userSearchForm" class="row g-3">
                <div class="col-md-6">
                    <label for="user_id" class="form-label">Enter Student/Staff User ID</label>
                    <input type="text" class="form-control" id="user_id" name="user_id" placeholder="User ID" required>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
            <div id="userDetails" class="mt-4"></div>
        </div>
    </div>
    <div id="actionSection"></div>
</div>
{% endblock %}
{% block extra_js %}
<script>
$(function() {
    $('#userSearchForm').on('submit', function(e) {
        e.preventDefault();
        var userId = $('#user_id').val().trim();
        if (!userId) return;
        $('#userDetails').html('<div class="text-center"><span class="spinner-border"></span> Loading...</div>');
        $.get('/librarian/api/user_details', { user_id: userId }, function(data) {
            if (data.success) {
                var html = '<div class="card"><div class="card-body">';
                html += '<h5>' + data.user.name + ' (' + data.user.designation + ')</h5>';
                html += '<p>User ID: <strong>' + data.user.user_id + '</strong></p>';
                html += '<p>Borrowed Books: <strong>' + data.borrowed.length + '</strong> / ' + data.limit + '</p>';
                html += '<ul class="list-group mb-3">';
                if (data.borrowed.length > 0) {
                    data.borrowed.forEach(function(book) {
                        html += '<li class="list-group-item d-flex justify-content-between align-items-center">';
                        html += book.title + ' <span class="badge bg-info">Due: ' + book.due_date + '</span>';
                        html += '<button class="btn btn-sm btn-success ms-2 return-btn" data-issue="' + book.issue_id + '">Return</button>';
                        html += '</li>';
                    });
                } else {
                    html += '<li class="list-group-item text-muted">No books currently borrowed.</li>';
                }
                html += '</ul>';
                html += '<div class="mb-2">';
                html += '<label for="book_search" class="form-label">Issue New Book</label>';
                html += '<div class="position-relative">';
                html += '<input type="text" class="form-control" id="book_search" placeholder="Search by book title or access number..." autocomplete="off">';
                html += '<input type="hidden" id="selected_book_id">';
                html += '<div id="book_suggestions" class="position-absolute w-100 bg-white border rounded shadow-sm" style="z-index: 1000; max-height: 200px; overflow-y: auto; display: none;"></div>';
                html += '</div>';
                html += '<button class="btn btn-primary mt-2" id="issueBtn" disabled>Issue Book</button>';
                html += '</div>';
                html += '</div></div>';
                $('#userDetails').html(html);
            } else {
                $('#userDetails').html('<div class="alert alert-danger">' + data.message + '</div>');
            }
        });
    });
    // Book search functionality
    $(document).on('input', '#book_search', function() {
        var query = $(this).val().trim();
        if (query.length >= 2) {
            $.get('/api/search_books_detailed', { q: query }, function(data) {
                var suggestions = $('#book_suggestions');
                suggestions.empty();

                if (data.length > 0) {
                    data.forEach(function(book) {
                        var item = $('<div class="p-2 border-bottom book-suggestion" style="cursor: pointer;">')
                            .attr('data-book-id', book.book_id)
                            .html('<strong>' + book.title + '</strong><br><small>Access No: ' + book.access_no + ' | Author: ' + book.author + ' | Available: ' + book.available_count + '</small>');

                        item.hover(function() {
                            $(this).addClass('bg-light');
                        }, function() {
                            $(this).removeClass('bg-light');
                        });

                        suggestions.append(item);
                    });
                    suggestions.show();
                } else {
                    suggestions.hide();
                }
            });
        } else {
            $('#book_suggestions').hide();
            $('#selected_book_id').val('');
            $('#issueBtn').prop('disabled', true);
        }
    });

    // Handle book selection
    $(document).on('click', '.book-suggestion', function() {
        var bookId = $(this).attr('data-book-id');
        var bookText = $(this).find('strong').text();
        var accessNo = $(this).text().match(/Access No: ([^\|]+)/)[1].trim();

        $('#book_search').val(bookText + ' (' + accessNo + ')');
        $('#selected_book_id').val(bookId);
        $('#book_suggestions').hide();
        $('#issueBtn').prop('disabled', false);
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#book_search, #book_suggestions').length) {
            $('#book_suggestions').hide();
        }
    });

    $(document).on('click', '#issueBtn', function() {
        var userId = $('#user_id').val().trim();
        var bookId = $('#selected_book_id').val();

        if (!bookId) {
            alert('Please select a book from the search results.');
            return;
        }

        $.post('/librarian/api/issue_book', { user_id: userId, book_id: bookId }, function(data) {
            if (data.success) {
                $('#userSearchForm').submit();
                alert('Book issued successfully!');
            } else {
                alert(data.message);
            }
        });
    });
    $(document).on('click', '.return-btn', function() {
        var issueId = $(this).data('issue');
        $.post('/librarian/api/return_book', { issue_id: issueId }, function(data) {
            if (data.success) {
                $('#userSearchForm').submit();
                alert('Book returned successfully!');
            } else {
                alert(data.message);
            }
        });
    });
});
</script>
{% endblock %}
