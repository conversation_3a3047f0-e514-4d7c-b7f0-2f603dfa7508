{% extends "base.html" %}

{% block title %}Reports - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('admin_reports') }}">
    <i class="fas fa-chart-bar me-2"></i>Reports
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-3"></i>Library Reports</h2>
</div>

<!-- Report Generation Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Generate Report</h5>
            </div>
            <div class="card-body">
                <form id="reportForm" method="POST" action="{{ url_for('admin_generate_report') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="report_type" class="form-label">Report Type</label>
                            <select class="form-select" id="report_type" name="report_type" required>
                                <option value="">Select Report Type</option>
                                <option value="issued_books">Currently Issued Books</option>
                                <option value="returned_books">Returned Books</option>
                                <option value="overdue_books">Overdue Books</option>
                                <option value="all_transactions">All Transactions</option>
                                <option value="fine_report">Fine Report</option>
                                <option value="student_activity">Student Activity Report</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="export_format" class="form-label">Export Format</label>
                            <select class="form-select" id="export_format" name="export_format" required>
                                <option value="">Select Format</option>
                                <option value="excel">Excel (.xlsx)</option>
                                <option value="pdf">PDF (.pdf)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3" id="dateRangeSection">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>
                    
                    <div class="row mb-3" id="additionalFilters">
                        <div class="col-md-6">
                            <label for="department" class="form-label">Department Filter</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">All Departments</option>
                                <option value="CSE">Computer Science Engineering</option>
                                <option value="IT">Information Technology</option>
                                <option value="ECE">Electronics & Communication</option>
                                <option value="EEE">Electrical & Electronics</option>
                                <option value="MECH">Mechanical Engineering</option>
                                <option value="CIVIL">Civil Engineering</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="designation" class="form-label">User Type Filter</label>
                            <select class="form-select" id="designation" name="designation">
                                <option value="">All Users</option>
                                <option value="Student">Students</option>
                                <option value="Staff">Staff</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>Generate & Download Report
                        </button>
                        <button type="button" class="btn btn-secondary" id="previewBtn">
                            <i class="fas fa-eye me-2"></i>Preview Report
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Report Information</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Report Types:</h6>
                    <ul class="mb-0 small">
                        <li><strong>Currently Issued:</strong> Books currently with students</li>
                        <li><strong>Returned Books:</strong> Books that have been returned</li>
                        <li><strong>Overdue Books:</strong> Books past their due date</li>
                        <li><strong>All Transactions:</strong> Complete issue/return history</li>
                        <li><strong>Fine Report:</strong> Students with outstanding fines</li>
                        <li><strong>Student Activity:</strong> Per-student borrowing statistics</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Note:</h6>
                    <p class="mb-0 small">Date range is optional for most reports. If not specified, all available data will be included.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Quick Reports</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_generate_report') }}?report_type=overdue_books&export_format=excel" class="btn btn-warning btn-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>Overdue Books (Excel)
                    </a>
                    <a href="{{ url_for('admin_generate_report') }}?report_type=issued_books&export_format=pdf" class="btn btn-info btn-sm">
                        <i class="fas fa-book me-2"></i>Currently Issued (PDF)
                    </a>
                    <a href="{{ url_for('admin_generate_report') }}?report_type=fine_report&export_format=excel" class="btn btn-danger btn-sm">
                        <i class="fas fa-money-bill me-2"></i>Fine Report (Excel)
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Section -->
<div id="previewSection" class="mt-4" style="display: none;">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Report Preview</h5>
        </div>
        <div class="card-body">
            <div id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Additional filters are always visible now for better filtering options
    
    // Set default dates (last 30 days)
    var today = new Date();
    var thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#end_date').val(today.toISOString().split('T')[0]);
    $('#start_date').val(thirtyDaysAgo.toISOString().split('T')[0]);
    
    // Preview functionality
    $('#previewBtn').click(function() {
        var formData = $('#reportForm').serialize() + '&preview=true';
        
        $.post('{{ url_for("admin_generate_report") }}', formData, function(data) {
            if (data.success) {
                $('#previewContent').html(data.html);
                $('#previewSection').show();
                $('html, body').animate({
                    scrollTop: $("#previewSection").offset().top
                }, 500);
            } else {
                alert('Error generating preview: ' + data.message);
            }
        }).fail(function() {
            alert('Error generating preview. Please check your selections.');
        });
    });
    
    // Form validation
    $('#reportForm').submit(function(e) {
        var reportType = $('#report_type').val();
        var exportFormat = $('#export_format').val();
        
        if (!reportType || !exportFormat) {
            e.preventDefault();
            alert('Please select both report type and export format.');
            return false;
        }
    });
});
</script>
{% endblock %}
