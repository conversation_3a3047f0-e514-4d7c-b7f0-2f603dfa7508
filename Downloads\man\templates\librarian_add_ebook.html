{% extends "base.html" %}

{% block title %}Add E-Book - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link active" href="{{ url_for('librarian_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>View Students
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_return_dashboard') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-3"></i>Add New E-Book</h2>
    <div>
        <a href="{{ url_for('librarian_bulk_ebooks') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-file-upload me-2"></i>Bulk Add E-Books
        </a>
        <a href="{{ url_for('librarian_ebooks') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to E-Books
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tablet-alt me-2"></i>E-Book Information</h5>
    </div>
    <div class="card-body">
        <form method="POST" class="row g-3">
            <!-- Basic Information -->
            <div class="col-12">
                <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="access_no" class="form-label">Access Number <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="access_no" name="access_no" required>
                <div class="form-text">Unique identifier for the e-book</div>
            </div>
            
            <div class="col-md-6">
                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="title" name="title" required>
            </div>
            
            <div class="col-md-6">
                <label for="author" class="form-label">Author <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="author" name="author" required>
            </div>
            
            <div class="col-md-6">
                <label for="publisher" class="form-label">Publisher <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="publisher" name="publisher" required>
            </div>
            
            <div class="col-md-6">
                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="subject" name="subject" required>
            </div>
            
            <div class="col-md-6">
                <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                <select class="form-select" id="department" name="department" required>
                    <option value="">Select Department</option>
                    {% for dept_code, dept_name in departments %}
                    <option value="{{ dept_code }}">{{ dept_code }} - {{ dept_name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                <select class="form-select" id="category" name="category" required>
                    <option value="">Select Category</option>
                    <option value="Textbook">Textbook</option>
                    <option value="Reference">Reference</option>
                    <option value="Research Paper">Research Paper</option>
                    <option value="Journal">Journal</option>
                    <option value="Magazine">Magazine</option>
                    <option value="Manual">Manual</option>
                    <option value="Guide">Guide</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <!-- Digital Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-digital-tachograph me-2"></i>Digital Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="file_format" class="form-label">File Format <span class="text-danger">*</span></label>
                <select class="form-select" id="file_format" name="file_format" required>
                    <option value="">Select Format</option>
                    <option value="PDF">PDF</option>
                    <option value="EPUB">EPUB</option>
                    <option value="MOBI">MOBI</option>
                    <option value="DOC">DOC</option>
                    <option value="DOCX">DOCX</option>
                    <option value="TXT">TXT</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="file_size" class="form-label">File Size</label>
                <input type="text" class="form-control" id="file_size" name="file_size" placeholder="e.g., 5.2 MB">
            </div>
            
            <div class="col-md-6">
                <label for="language" class="form-label">Language</label>
                <select class="form-select" id="language" name="language">
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Tamil">Tamil</option>
                    <option value="Telugu">Telugu</option>
                    <option value="Malayalam">Malayalam</option>
                    <option value="Kannada">Kannada</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="pages" class="form-label">Number of Pages</label>
                <input type="number" class="form-control" id="pages" name="pages" min="1">
            </div>
            
            <div class="col-12">
                <label for="download_url" class="form-label">Download URL/File Path</label>
                <input type="url" class="form-control" id="download_url" name="download_url" placeholder="https://example.com/ebook.pdf">
                <div class="form-text">URL where the e-book can be downloaded or accessed</div>
            </div>
            
            <!-- Additional Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-plus-circle me-2"></i>Additional Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="isbn" class="form-label">ISBN</label>
                <input type="text" class="form-control" id="isbn" name="isbn" placeholder="978-0-123456-78-9">
            </div>
            
            <div class="col-12">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Brief description of the e-book content..."></textarea>
            </div>
            
            <div class="col-12 text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>Add E-Book
                </button>
                <button type="reset" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>Clear
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate subject based on department
    $('#department').change(function() {
        const department = $(this).val();
        const departmentText = $(this).find('option:selected').text();
        
        if (department && !$('#subject').val()) {
            const subjectName = departmentText.split(' - ')[1];
            $('#subject').val(subjectName);
        }
    });
    
    // Auto-generate access number for e-books
    function loadNextEbookAccessNumber() {
        $.get('/api/next_ebook_access_number', function(data) {
            if (data.success) {
                $('#access_no').val('E' + data.next_access_number);
            }
        });
    }
    
    // Load next access number on page load
    loadNextEbookAccessNumber();
    
    // Also load when access number field is focused and empty
    $('#access_no').on('focus', function() {
        if (!$(this).val()) {
            loadNextEbookAccessNumber();
        }
    });
});
</script>
{% endblock %}
