{% extends "base.html" %}

{% block title %}Renew Book - Library Management System{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') if session.user_role == 'admin' else url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Circulation Submenu -->
<div class="nav-item">
    <a class="nav-link" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_book') }}">
                <i class="fas fa-arrow-right me-2"></i>Issue Book
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_return_book') }}">
                <i class="fas fa-arrow-left me-2"></i>Return Book
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('circulation_renew_book') }}">
                <i class="fas fa-redo me-2"></i>Renew Book
            </a>
            <a class="nav-link py-1" href="{{ url_for('circulation_reservation') }}">
                <i class="fas fa-calendar-check me-2"></i>Reservation
            </a>
            <a class="nav-link py-1" href="{{ url_for('circulation_member_search') }}">
                <i class="fas fa-search me-2"></i>Member Search
            </a>
            <a class="nav-link py-1" href="{{ url_for('circulation_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<a class="nav-link" href="{{ url_for('admin_books') if session.user_role == 'admin' else url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_students') if session.user_role == 'admin' else url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-redo me-3 text-primary"></i>Renew Book</h2>
    <div>
        <button class="btn btn-outline-primary btn-custom" onclick="refreshPage()">
            <i class="fas fa-refresh me-2"></i>Refresh
        </button>
    </div>
</div>

<!-- Search Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search by Student</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="studentSearch" class="form-label">Student ID / Name / Email</label>
                    <input type="text" class="form-control" id="studentSearch" placeholder="Enter student details...">
                </div>
                <button type="button" class="btn btn-primary" onclick="searchStudent()">
                    <i class="fas fa-search me-2"></i>Search Student
                </button>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>Quick Scan</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="bookBarcode" class="form-label">Book Barcode / Access Number</label>
                    <input type="text" class="form-control" id="bookBarcode" placeholder="Scan or enter barcode...">
                </div>
                <button type="button" class="btn btn-success" onclick="scanBook()">
                    <i class="fas fa-barcode me-2"></i>Find Book
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Renewal Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Books Available for Renewal</h5>
    </div>
    <div class="card-body">
        <div id="renewalResults">
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>Search for a student or scan a book to see renewal options</p>
            </div>
        </div>
    </div>
</div>

<!-- Renewal History -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Renewals</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Student</th>
                        <th>Book</th>
                        <th>Previous Due Date</th>
                        <th>New Due Date</th>
                        <th>Renewal Count</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="renewalHistoryTable">
                    <tr>
                        <td colspan="7" class="text-center text-muted">No recent renewals</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshPage() {
    location.reload();
}

function searchStudent() {
    const searchTerm = document.getElementById('studentSearch').value.trim();
    if (!searchTerm) {
        showToast('Please enter student details to search', 'error');
        return;
    }
    
    // Show loading
    document.getElementById('renewalResults').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Searching for student...</p>
        </div>
    `;
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        displayRenewalOptions([
            {
                issueId: 1,
                bookTitle: "Introduction to Algorithms",
                author: "Thomas H. Cormen",
                accessNo: "CS001",
                issueDate: "2024-01-15",
                dueDate: "2024-01-29",
                renewalCount: 0,
                maxRenewals: 2,
                canRenew: true
            }
        ]);
    }, 1000);
}

function scanBook() {
    const barcode = document.getElementById('bookBarcode').value.trim();
    if (!barcode) {
        showToast('Please enter or scan a barcode', 'error');
        return;
    }
    
    // Show loading
    document.getElementById('renewalResults').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Finding book...</p>
        </div>
    `;
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        showToast('Book found! Displaying renewal options.', 'success');
        displayRenewalOptions([
            {
                issueId: 1,
                bookTitle: "Introduction to Algorithms",
                author: "Thomas H. Cormen",
                accessNo: barcode,
                issueDate: "2024-01-15",
                dueDate: "2024-01-29",
                renewalCount: 0,
                maxRenewals: 2,
                canRenew: true
            }
        ]);
    }, 1000);
}

function displayRenewalOptions(books) {
    if (!books || books.length === 0) {
        document.getElementById('renewalResults').innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                <p>No books found for renewal</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="row">';
    books.forEach(book => {
        const daysUntilDue = Math.ceil((new Date(book.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
        const statusClass = daysUntilDue < 0 ? 'danger' : daysUntilDue <= 3 ? 'warning' : 'success';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${statusClass}">
                    <div class="card-body">
                        <h6 class="card-title">${book.bookTitle}</h6>
                        <p class="card-text">
                            <strong>Author:</strong> ${book.author}<br>
                            <strong>Access No:</strong> ${book.accessNo}<br>
                            <strong>Issue Date:</strong> ${book.issueDate}<br>
                            <strong>Due Date:</strong> ${book.dueDate}<br>
                            <strong>Renewals:</strong> ${book.renewalCount}/${book.maxRenewals}
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-${statusClass}">
                                ${daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} days overdue` : 
                                  daysUntilDue === 0 ? 'Due today' : 
                                  `${daysUntilDue} days remaining`}
                            </span>
                            ${book.canRenew ? 
                                `<button class="btn btn-primary btn-sm" onclick="renewBook(${book.issueId})">
                                    <i class="fas fa-redo me-1"></i>Renew
                                </button>` :
                                `<button class="btn btn-secondary btn-sm" disabled>
                                    <i class="fas fa-ban me-1"></i>Cannot Renew
                                </button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    document.getElementById('renewalResults').innerHTML = html;
}

function renewBook(issueId) {
    if (!confirm('Are you sure you want to renew this book?')) {
        return;
    }
    
    // Simulate API call - replace with actual implementation
    showToast('Book renewed successfully!', 'success');
    
    // Refresh the search results
    searchStudent();
}
</script>
{% endblock %}
