{% extends "base.html" %}

{% block title %}Student Details - {{ student.name }}{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link active" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user me-3"></i>Student Details</h2>
    <div>
        <a href="{{ url_for('admin_students') }}" class="btn btn-secondary btn-custom me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to Students
        </a>
        <button class="btn btn-warning btn-custom" onclick="editStudent({{ student.id }})">
            <i class="fas fa-edit me-2"></i>Edit Student
        </button>
    </div>
</div>

<!-- Student Information Card -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Personal Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>User ID:</strong></td>
                                <td>{{ student.user_id or student.id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ student.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td><code>{{ student.username or student.email }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Roll Number:</strong></td>
                                <td>{{ student.roll_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ student.email }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Department:</strong></td>
                                <td><span class="badge bg-info">{{ student.department or 'N/A' }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Designation:</strong></td>
                                <td>
                                    <span class="badge {% if student.designation == 'Staff' %}bg-warning{% else %}bg-primary{% endif %}">
                                        {{ student.designation or 'Student' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Course:</strong></td>
                                <td>{{ student.course or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Current Year:</strong></td>
                                <td>{{ student.current_year or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date of Birth:</strong></td>
                                <td>
                                    {% if student.dob %}
                                        {{ student.dob.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Validity Date:</strong></td>
                                <td>
                                    {% if student.validity_date %}
                                        {{ student.validity_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="col-lg-4">
        <div class="row">
            <div class="col-12 mb-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-book fa-2x text-primary mb-2"></i>
                        <h4>{{ active_issues|length }}</h4>
                        <p class="text-muted mb-0">Active Issues</p>
                    </div>
                </div>
            </div>
            <div class="col-12 mb-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h4>{{ overdue_count }}</h4>
                        <p class="text-muted mb-0">Overdue Books</p>
                    </div>
                </div>
            </div>
            <div class="col-12 mb-3">
                <div class="card stat-card stat-card-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-rupee-sign fa-2x text-danger mb-2"></i>
                        <h4>₹{{ "%.2f"|format(total_fine) }}</h4>
                        <p class="text-muted mb-0">Total Fines</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Currently Borrowed Books -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-book-open me-2"></i>Currently Borrowed Books</h5>
    </div>
    <div class="card-body">
        {% if active_issues %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Book Details</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Days Remaining</th>
                        <th>Status</th>
                        <th>Fine</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in active_issues %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ issue.book.title }}</strong>
                                <br>
                                <small class="text-muted">
                                    Access No: {{ issue.book.access_no }} | 
                                    Author: {{ issue.book.author }}
                                </small>
                            </div>
                        </td>
                        <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if issue.due_date %}
                                {{ issue.due_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.due_date %}
                                <span class="text-info">Check due date</span>
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.due_date %}
                                <span class="badge bg-info">Active</span>
                            {% else %}
                                <span class="badge bg-info">Active</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.fine_amount and issue.fine_amount > 0 %}
                                <span class="text-danger">₹{{ "%.2f"|format(issue.fine_amount) }}</span>
                            {% else %}
                                <span class="text-success">₹0.00</span>
                            {% endif %}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-success me-1" 
                                    onclick="returnBook({{ issue.id }})" 
                                    title="Mark as Returned">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" 
                                    onclick="renewBook({{ issue.id }})" 
                                    title="Renew Book">
                                <i class="fas fa-redo"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No books currently borrowed</h5>
            <p class="text-muted">This student has no active book issues.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Issue History -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Issue History</h5>
    </div>
    <div class="card-body">
        {% if issue_history %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Book</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Return Date</th>
                        <th>Status</th>
                        <th>Fine</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issue_history %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ issue.book.title }}</strong>
                                <br>
                                <small class="text-muted">Access No: {{ issue.book.access_no }}</small>
                            </div>
                        </td>
                        <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if issue.due_date %}
                                {{ issue.due_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.return_date %}
                                {{ issue.return_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-warning">Not returned</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.return_date %}
                                <span class="badge bg-success">Returned</span>
                            {% else %}
                                <span class="badge bg-warning">Active</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.fine_amount and issue.fine_amount > 0 %}
                                <span class="text-danger">₹{{ "%.2f"|format(issue.fine_amount) }}</span>
                            {% else %}
                                <span class="text-success">₹0.00</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-history fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No issue history</h5>
            <p class="text-muted">This student has never borrowed any books.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editStudent(id) {
    window.location.href = `/admin/edit_student/${id}`;
}

function returnBook(issueId) {
    if (confirm('Mark this book as returned?')) {
        // TODO: Implement return book functionality
        // This would make a POST request to return the book
        fetch(`/admin/return_book/${issueId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error returning book');
            }
        })
        .catch(error => {
            alert('Error returning book');
        });
    }
}

function renewBook(issueId) {
    if (confirm('Renew this book issue?')) {
        // TODO: Implement renew book functionality
        fetch(`/admin/renew_book/${issueId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error renewing book');
            }
        })
        .catch(error => {
            alert('Error renewing book');
        });
    }
}
</script>
{% endblock %}
